import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import { <PERSON>rowser<PERSON>outer } from 'react-router-dom'
import { ToastContainer } from 'react-toastify'
import RecepiContext from './context/RecepiContext'


createRoot(document.getElementById('root')).render(
  <RecepiContext>
    <BrowserRouter>
      <App />
      <ToastContainer />
    </BrowserRouter>
  </RecepiContext>
)
