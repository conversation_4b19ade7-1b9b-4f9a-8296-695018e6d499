# 🍳 Recipe App

A modern, responsive recipe management application built with React and Vite. Create, manage, and favorite your culinary masterpieces with a beautiful dark-themed interface.

## ✨ Features

### 🏠 **Home Page**
- Welcome landing page with navigation to recipes and creation
- Modern gradient design with smooth animations
- Responsive layout for all devices

### 📖 **Recipe Management**
- **View All Recipes**: Browse through your recipe collection in a beautiful card layout
- **Create New Recipes**: Add new recipes with detailed information
- **Edit Recipes**: Update existing recipes with form validation
- **Delete Recipes**: Remove recipes with confirmation
- **Recipe Details**: View complete recipe information on dedicated pages

### ❤️ **Favorite System**
- **Heart Icons**: Click heart icons to add/remove recipes from favorites
- **Visual Feedback**: Filled hearts for favorited recipes, outline hearts for unfavorited
- **Quick Actions**: Favorite recipes directly from recipe cards or detail pages
- **Persistent Storage**: Favorites are saved in localStorage and persist across sessions
- **Toast Notifications**: Success messages when adding/removing favorites

### 🎨 **User Interface**
- **Dark Theme**: Modern dark gray and black color scheme with red accents
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Smooth Animations**: Hover effects, transitions, and micro-interactions
- **Form Validation**: Real-time validation with helpful error messages
- **Toast Notifications**: User feedback for all actions

### 💾 **Data Persistence**
- **localStorage Integration**: All recipes and favorites are automatically saved
- **Real-time Updates**: Changes are immediately reflected across the application
- **Data Recovery**: Recipes persist between browser sessions

## 🛠️ Tech Stack

- **Frontend**: React 18 with Hooks
- **Build Tool**: Vite for fast development and building
- **Routing**: React Router DOM for navigation
- **Forms**: React Hook Form for form management and validation
- **Styling**: Tailwind CSS for responsive design
- **Icons**: Heroicons (SVG icons)
- **Notifications**: React Toastify for user feedback
- **State Management**: React Context API
- **ID Generation**: Nanoid for unique recipe IDs

## 📁 Project Structure

```
src/
├── components/
│   └── RecipeCard.jsx          # Recipe card component with favorite functionality
├── context/
│   └── RecepiContext.jsx       # Global state management and localStorage
├── pages/
│   ├── Home.jsx               # Landing page
│   ├── Recepies.jsx           # Recipe listing page
│   ├── Create.jsx             # Recipe creation form
│   └── SingleRecipe.jsx       # Recipe detail and edit page
├── App.jsx                    # Main app component with routing
└── main.jsx                   # Application entry point
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Yashaswirai/recipe-App.git
   cd recipe-App
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173` (or the port shown in terminal)

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## 📱 Usage

### Creating a Recipe
1. Navigate to "Create Recipe" from the home page or navigation
2. Fill in all required fields:
   - Recipe Name
   - Recipe Type (Vegetarian/Non-Vegetarian)
   - Chef Name
   - Recipe Image URL
   - Description
   - Ingredients (comma-separated)
3. Click "Create Recipe" to save

### Managing Favorites
- **From Recipe Cards**: Click the heart icon in the top-right corner
- **From Recipe Details**: Click the "Add to Favorites" button in the chef section
- **Visual Indicators**: Filled red hearts indicate favorited recipes

### Editing Recipes
1. Click on any recipe card to view details
2. Scroll down to the edit form
3. Modify any fields as needed
4. Click "Update Recipe" to save changes

## 🎨 Design Features

- **Color Scheme**: Dark theme with gray backgrounds and red accent colors
- **Typography**: Clean, readable fonts with proper hierarchy
- **Cards**: Elevated cards with hover effects and smooth transitions
- **Forms**: Well-structured forms with validation and error handling
- **Buttons**: Gradient buttons with hover animations
- **Icons**: Consistent iconography throughout the application

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Key Dependencies

```json
{
  "react": "^18.x.x",
  "react-dom": "^18.x.x",
  "react-router-dom": "^6.x.x",
  "react-hook-form": "^7.x.x",
  "react-toastify": "^9.x.x",
  "nanoid": "^4.x.x"
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 👨‍💻 Author

**Yashaswi Rai**
- GitHub: [@Yashaswirai](https://github.com/Yashaswirai)
- Email: <EMAIL>

---

Built with ❤️ using React and Vite
