import React, { useEffect, useState } from 'react';
export const dataContext = React.createContext(null);
const RecepiContext = (props) => {
  const [data, setData] = useState([]);

  useEffect(() => {
    const data = localStorage.getItem('recipes');
    if (data) {
      setData(JSON.parse(data));
    }
  }, []);
  useEffect(() => {
    localStorage.setItem('recipes', JSON.stringify(data));
  }, [data]);

  return (
    <dataContext.Provider value={{ data, setData }}>
      {props.children}
    </dataContext.Provider>
  );
};

export default RecepiContext;



    // {
    //   chef: "Yashaswi Rai",
    //   description:
    //     "Tandoori butter chicken masala is a lip smacking recipe with the crispness of chicken and the tangy and spicy flavour of gravy which compliments well as a main dish..This recipe is the fav. in my family..Do try it.",
    //   id: "p7_qCNAv9W7x9PudSTXq8",
    //   image:
    //     "https://img-global.cpcdn.com/recipes/51e4d8d720317260/1360x1928cq90/tandoori-butter-chicken-masala-recipe-main-photo.webp",
    //   ingredients:
    //     "400 gm chicken,To taste Salt,2 pinch Black pepper,2 tsp Lemon juice,1 tsp Coriander powder,1 tsp cumin powder",
    //   name: "Tandoori Butter Masala",
    //   type: "non-veg",
    // },